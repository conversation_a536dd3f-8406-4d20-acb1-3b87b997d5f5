{"name": "beckwell-online-learning", "version": "1.0.0", "scripts": {"dev": "vite", "dev:stage": "vite --mode staging", "build:hwprod": "vite build --mode hwprod && node huawei-obs-upload.js eduuser eduuser1", "build:prod": "vite build --mode prod", "build:xdprod": "vite build --mode xdprod && node huawei-obs-upload.js eduxduser", "build:stage": "vite build --mode staging", "preview": "vite preview --port 5050", "typecheck": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.0.10", "@js-preview/docx": "^1.6.4", "@js-preview/excel": "^1.7.14", "@js-preview/pdf": "^2.0.10", "@vueuse/core": "^7.6.2", "axios": "^0.26.0", "dayjs": "^1.10.7", "echarts": "^5.4.3", "element-plus": "^2.2.19", "esbuild-windows-64": "^0.15.18", "esdk-obs-browserjs": "^3.24.3", "file-saver": "^2.0.5", "goldenzqqq-vue3-video-play": "^1.0.4", "js-base64": "^3.7.5", "js-cookie": "^3.0.1", "jsencrypt": "^3.3.2", "less": "^4.1.2", "lodash-es": "^4.17.21", "normalize.css": "^8.0.1", "pinia": "^2.0.11", "pinia-plugin-persistedstate": "^1.2.2", "pptx2html": "^0.3.4", "qrcode.vue": "^3.6.0", "smooth-signature": "^1.0.13", "vue": "^3.2.45", "vue-router": "^4.0.12", "vue3-scroll-seamless": "^1.0.6"}, "devDependencies": {"@types/node": "^16.11.22", "@types/video.js": "^7.3.49", "@unocss/runtime": "^0.64.0", "@vitejs/plugin-vue": "^3.1.0", "@vitejs/plugin-vue-jsx": "^1.3.3", "@vue/tsconfig": "^0.1.3", "html2canvas": "^1.4.1", "huawei-obs-plugin": "^1.0.2", "prettier": "^2.5.1", "typescript": "~4.5.5", "unocss": "0.53.6", "unplugin-auto-import": "^0.16.6", "video.js": "^7.20.3", "vite": "^3.2.3", "vite-ali-oss-plugin": "^1.1.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-tsc": "^0.31.1"}}