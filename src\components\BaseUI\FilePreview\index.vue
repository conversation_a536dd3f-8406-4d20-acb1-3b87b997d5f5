<!--
 * @Description: 文件预览
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-10 13:14:50
 * @LastEditTime: 2025-07-22 16:55:32
-->

<template>
  <el-dialog
    class="filePreviewDialog"
    v-model="dialogFormVisible"
    :close-on-click-modal="false"
    width="80%"
    @close="close"
    :key="new Date().getTime()"
    align-center
    top="5vh"
  >
    <template #header="{ close, titleId, titleClass }">
      <div class="dialog-header">
        <h2 :id="titleId" :class="titleClass">{{ title }}</h2>
        <div class="downloadBtn">
          <i
            v-if="meansItem?.isDownload === '1'"
            @click="download"
            class="iconfont icon-xiazai"
          ></i>
          <i
            @click="support"
            class="iconfont icon-xihuan"
            :class="likeFlag ? 'active-collect' : ''"
          ></i>
        </div>
      </div>
    </template>

    <!-- 只支持.docx格式，.doc格式使用iframe预览 -->
    <div v-if="dialogFormVisible && downloadUrl" class="office-preview-container">
      <!-- 如果组件渲染失败，使用iframe预览 -->
      <iframe
        v-if="useIframePreview"
        ref="dynamicIframe"
        frameborder="0"
        :src="iframeUrl"
        style="width: 100%; height: calc(100vh - 60px)"
      />
      <!-- 优先使用@vue-office组件 -->
      <template v-else>
        <vue-office-docx
          v-if="isDocxFile(downloadUrl)"
          :key="downloadUrl"
          :src="downloadUrl"
          style="height: calc(100vh - 60px)"
          @rendered="renderedHandler"
          @error="errorHandler"
        />
        <vue-office-pptx
          v-if="isPptxFile(downloadUrl)"
          :key="downloadUrl"
          :src="downloadUrl"
          style="height: calc(100vh - 60px)"
          @rendered="renderedHandler"
          @error="errorHandler"
        />
        <vue-office-excel
          v-else-if="isExcelFile(downloadUrl)"
          :key="downloadUrl"
          :src="downloadUrl"
          style="height: calc(100vh - 60px)"
          @rendered="renderedHandler"
          @error="errorHandler"
        />
        <vue-office-pdf
          v-else-if="isPdfFile(downloadUrl)"
          :key="downloadUrl"
          :src="downloadUrl"
          style="height: calc(100vh - 60px)"
          @rendered="renderedHandler"
          @error="errorHandler"
        />
        <div v-else-if="isImgPath(downloadUrl)" style="text-align: center">
          <el-image :src="downloadUrl" style="height: calc(100vh - 60px)" />
        </div>
        <iframe
          v-else
          ref="dynamicIframe"
          frameborder="0"
          :src="iframeUrl"
          style="width: 100%; height: calc(100vh - 60px)"
        />
      </template>
    </div>
  </el-dialog>
</template>
<script setup lang="ts" name="FilePreview">
  import VueOfficePptx from '@vue-office/pptx'
  import VueOfficeDocx from '@vue-office/docx'
  import VueOfficeExcel from '@vue-office/excel'
  import VueOfficePdf from '@vue-office/pdf'
  import { isDocxFile, isExcelFile, isPdfFile, isPptxFile, isImgPath } from '@/utils'
  import '@vue-office/docx/lib/index.css'
  import '@vue-office/excel/lib/index.css'
  import { editRelateNumber } from "@/api/trainingTask/means"
  import { ElMessage } from "element-plus"
  import { ref, nextTick } from "vue"
  import { Base64 } from "js-base64"
  import type { MeansItemType } from "@/types"

  const title = ref("文件预览")
  const dialogFormVisible = ref(false)
  const iframeUrl = ref("")
  const likeFlag = ref(false)
  const meansItem = ref<MeansItemType>()
  const downloadUrl = ref("")
  const primaryKeyId = ref("")

  const kkFileURL = import.meta.env.VITE_APP_KK_URL

  // const videoFormats = ["mp4", "avi", "mov", "wmv"]
  const fileLoad = (item: any) => {
    meansItem.value = item
    likeFlag.value = item.likeFlag
    dialogFormVisible.value = true

    // 使用nextTick确保DOM已更新
    nextTick(() => {
      downloadUrl.value = item.manageAddress
      if (item.manageId) primaryKeyId.value = item.manageId
      // 判断是否是视频格式
      // const isVideo = new RegExp(`.(${videoFormats.join("|")})$`, "i").test(docUrl)
      // if (isVideo)
      iframeUrl.value =
        `${kkFileURL}/onlinePreview?url=` + encodeURIComponent(Base64.encode(item.manageAddress))
    })
  }

  interface Emit {
    (e: "clickDownload", value?: any): void
    (e: "closeDialog", value?: any): void
  }
  const emit = defineEmits<Emit>()
  const download = () => {
    window.open(downloadUrl.value, "_blank")
    emit("clickDownload", primaryKeyId.value)
  }

  const close = () => {
    // 清空数据，避免组件渲染问题
    downloadUrl.value = ""
    iframeUrl.value = ""
    useIframePreview.value = false
    dialogFormVisible.value = false
    emit("closeDialog")
  }

  const support = async () => {
    if (likeFlag.value) {
      await editRelateNumber({ manageId: primaryKeyId.value, likeNumber: -1 })
      ElMessage({
        message: "取消点赞成功！",
        type: "success"
      })
    } else {
      await editRelateNumber({ manageId: primaryKeyId.value, likeNumber: 1 })
      ElMessage({
        message: "点赞成功！",
        type: "success"
      })
    }
    likeFlag.value = !likeFlag.value
  }

  // 渲染完成的回调
  const renderedHandler = () => {
    console.log("文档渲染完成")
  }

  // 渲染失败的回调
  const errorHandler = (e) => {
    console.log('[ e ] >', e)
    console.log("文档渲染失败，回退到iframe预览")
    ElMessage({
      message: "使用备用预览方式",
      type: "warning"
    })
    // 回退到iframe预览
    useIframePreview.value = true
  }

  // 是否使用iframe预览的标志
  const useIframePreview = ref(false)

  defineExpose({
    fileLoad
  })
</script>

<style scoped lang="less">
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .downloadBtn {
      margin-right: 30px;
      vertical-align: middle;
      > i {
        margin-right: 50px;
        cursor: pointer;
      }
    }
  }

  .active-collect {
    color: #dc4853 !important;
    transition: all 0.3s;
  }
  .icon-xiazai {
    color: #2695f9;
  }
  .icon-xihuan {
    color: #9c9c9c;
  }

  .office-preview-container {
    width: 100%;
    height: calc(100vh - 60px);
    position: relative;
  }
</style>
