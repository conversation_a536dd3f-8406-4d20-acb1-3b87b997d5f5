<!--
 * @Description: 文件预览
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-10 13:14:50
 * @LastEditTime: 2025-07-22 16:15:47
-->

<template>
  <el-dialog
    class="filePreviewDialog"
    v-model="dialogFormVisible"
    :close-on-click-modal="false"
    width="80%"
    @close="close"
    :key="new Date().getTime()"
    align-center
    top="5vh"
  >
    <template #header="{ close, titleId, titleClass }">
      <div class="dialog-header">
        <h2 :id="titleId" :class="titleClass">{{ title }}</h2>
        <div class="downloadBtn">
          <i
            v-if="meansItem?.isDownload === '1'"
            @click="download"
            class="iconfont icon-xiazai"
          ></i>
          <i
            @click="support"
            class="iconfont icon-xihuan"
            :class="likeFlag ? 'active-collect' : ''"
          ></i>
        </div>
      </div>
    </template>

    <!-- 只支持.docx格式，.doc格式使用iframe预览 -->
    <vue-office-docx
      v-if="isDocxFile(downloadUrl) && dialogFormVisible"
      :src="downloadUrl"
      style="height: calc(100vh - 60px)"
      @rendered="renderedHandler"
      @error="errorHandler"
    />
    <vue-office-excel
      v-else-if="isExcelFile(downloadUrl) && dialogFormVisible"
      :src="downloadUrl"
      style="height: calc(100vh - 60px)"
      @rendered="renderedHandler"
      @error="errorHandler"
    />
    <vue-office-pdf
      v-else-if="isPdfFile(downloadUrl) && dialogFormVisible"
      :src="downloadUrl"
      style="height: calc(100vh - 60px)"
      @rendered="renderedHandler"
      @error="errorHandler"
    />
    <div v-else-if="isImgPath(downloadUrl)" style="text-align: center">
      <el-image :src="downloadUrl" style="height: calc(100vh - 60px)" />
    </div>
    <iframe
      v-else
      ref="dynamicIframe"
      frameborder="0"
      :src="iframeUrl"
      style="width: 100%; height: 80vh"
    />
  </el-dialog>
</template>
<script setup lang="ts" name="FilePreview">
  import VueOfficeDocx from '@vue-office/docx'
  import VueOfficeExcel from '@vue-office/excel'
  import VueOfficePdf from '@vue-office/pdf'
  import { isDocxFile, isExcelFile, isPdfFile, isDocFile, isImgPath } from '@/utils'
  import '@vue-office/docx/lib/index.css'
  import '@vue-office/excel/lib/index.css'
  import { editRelateNumber } from "@/api/trainingTask/means"
  import { ElMessage } from "element-plus"
  import { ref } from "vue"
  import { Base64 } from "js-base64"
  import type { MeansItemType } from "@/types"

  const title = ref("文件预览")
  const dialogFormVisible = ref(false)
  const iframeUrl = ref("")
  const likeFlag = ref(false)
  const meansItem = ref<MeansItemType>()
  const downloadUrl = ref("")
  const primaryKeyId = ref("")

  const kkFileURL = import.meta.env.VITE_APP_KK_URL

  // const videoFormats = ["mp4", "avi", "mov", "wmv"]
  const fileLoad = item => {
    meansItem.value = item
    likeFlag.value = item.likeFlag
    dialogFormVisible.value = true
    downloadUrl.value = item.manageAddress
    if (item.manageId) primaryKeyId.value = item.manageId
    // 判断是否是视频格式
    // const isVideo = new RegExp(`.(${videoFormats.join("|")})$`, "i").test(docUrl)
    // if (isVideo)
    iframeUrl.value =
      `${kkFileURL}/onlinePreview?url=` + encodeURIComponent(Base64.encode(item.manageAddress))
  }

  interface Emit {
    (e: "clickDownload", value?: any): void
    (e: "closeDialog", value?: any): void
  }
  const emit = defineEmits<Emit>()
  const download = () => {
    window.open(downloadUrl.value, "_blank")
    emit("clickDownload", primaryKeyId.value)
  }

  const close = () => {
    dialogFormVisible.value = false
    emit("closeDialog")
  }

  const support = async () => {
    if (likeFlag.value) {
      await editRelateNumber({ manageId: primaryKeyId.value, likeNumber: -1 })
      ElMessage({
        message: "取消点赞成功！",
        type: "success"
      })
    } else {
      await editRelateNumber({ manageId: primaryKeyId.value, likeNumber: 1 })
      ElMessage({
        message: "点赞成功！",
        type: "success"
      })
    }
    likeFlag.value = !likeFlag.value
  }

  // 渲染完成的回调
  const renderedHandler = () => {
    console.log("文档渲染完成")
  }

  // 渲染失败的回调
  const errorHandler = () => {
    console.log("文档渲染失败")
    ElMessage({
      message: "文档加载失败，请重试",
      type: "error"
    })
  }

  defineExpose({
    fileLoad
  })
</script>

<style scoped lang="less">
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .downloadBtn {
      margin-right: 30px;
      vertical-align: middle;
      > i {
        margin-right: 50px;
        cursor: pointer;
      }
    }
  }

  .active-collect {
    color: #dc4853 !important;
    transition: all 0.3s;
  }
  .icon-xiazai {
    color: #2695f9;
  }
  .icon-xihuan {
    color: #9c9c9c;
  }
</style>
