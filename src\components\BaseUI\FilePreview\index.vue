<!--
 * @Description: 文件预览
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-10 13:14:50
 * @LastEditTime: 2025-07-23 08:48:38
-->

<template>
  <el-dialog
    class="filePreviewDialog"
    v-model="dialogFormVisible"
    :close-on-click-modal="false"
    width="80%"
    @close="close"
    :key="new Date().getTime()"
    align-center
    top="5vh"
  >
    <template #header="{ close, titleId, titleClass }">
      <div class="dialog-header">
        <h2 :id="titleId" :class="titleClass">{{ title }}</h2>
        <div class="downloadBtn">
          <i
            v-if="meansItem?.isDownload === '1'"
            @click="download"
            class="iconfont icon-xiazai"
          ></i>
          <i
            @click="support"
            class="iconfont icon-xihuan"
            :class="likeFlag ? 'active-collect' : ''"
          ></i>
        </div>
      </div>
    </template>

    <!-- 使用@js-preview进行文件预览 -->
    <div v-if="dialogFormVisible && downloadUrl" class="office-preview-container">
      <!-- 如果js-preview渲染失败，使用iframe预览 -->
      <iframe
        v-if="useIframePreview"
        ref="dynamicIframe"
        frameborder="0"
        :src="iframeUrl"
        style="width: 100%; height: calc(100vh - 60px)"
      />
      <!-- 优先使用@js-preview组件 -->
      <template v-else>
        <!-- docx文件预览容器 -->
        <div
          v-if="isDocxFile(downloadUrl)"
          ref="docxContainer"
          class="js-preview-container"
        ></div>
        <!-- excel文件预览容器 -->
        <div
          v-else-if="isExcelFile(downloadUrl)"
          ref="excelContainer"
          class="js-preview-container"
        ></div>
        <!-- pdf文件预览容器 -->
        <div
          v-else-if="isPdfFile(downloadUrl)"
          ref="pdfContainer"
          class="js-preview-container"
        ></div>
        <!-- pptx文件预览容器 -->
        <div
          v-else-if="isPptxFile(downloadUrl)"
          ref="pptxContainer"
          class="js-preview-container pptx-container"
        ></div>
        <!-- 图片预览 -->
        <div v-else-if="isImgPath(downloadUrl)" style="text-align: center">
          <el-image :src="downloadUrl" style="height: calc(100vh - 60px)" />
        </div>
        <!-- 其他文件使用iframe预览 -->
        <iframe
          v-else
          ref="dynamicIframe"
          frameborder="0"
          :src="iframeUrl"
          style="width: 100%; height: calc(100vh - 60px)"
        />
      </template>
    </div>
  </el-dialog>
</template>
<script setup lang="ts" name="FilePreview">
  import jsPreviewDocx from '@js-preview/docx'
  import jsPreviewExcel from '@js-preview/excel'
  import jsPreviewPdf from '@js-preview/pdf'

  import '@js-preview/docx/lib/index.css'
  import '@js-preview/excel/lib/index.css'
  import { isDocxFile, isExcelFile, isPdfFile, isPptxFile, isImgPath } from '@/utils'
  import { editRelateNumber } from "@/api/trainingTask/means"
  import { ElMessage } from "element-plus"
  import { ref, nextTick, onUnmounted } from "vue"
  import { Base64 } from "js-base64"
  import type { MeansItemType } from "@/types"

  const title = ref("文件预览")
  const dialogFormVisible = ref(false)
  const iframeUrl = ref("")
  const likeFlag = ref(false)
  const meansItem = ref<MeansItemType>()
  const downloadUrl = ref("")
  const primaryKeyId = ref("")

  // DOM引用
  const docxContainer = ref<HTMLElement>()
  const excelContainer = ref<HTMLElement>()
  const pdfContainer = ref<HTMLElement>()
  const pptxContainer = ref<HTMLElement>()
  const dynamicIframe = ref<HTMLElement>()

  // 预览器实例
  let docxPreviewer: any = null
  let excelPreviewer: any = null
  let pdfPreviewer: any = null

  const kkFileURL = import.meta.env.VITE_APP_KK_URL

  // 初始化预览器
  const initPreviewer = async (fileUrl: string) => {
    try {
      if (isDocxFile(fileUrl) && docxContainer.value) {
        // 清理之前的预览器
        if (docxPreviewer) {
          docxPreviewer.destroy?.()
        }
        docxPreviewer = jsPreviewDocx.init(docxContainer.value)
        await docxPreviewer.preview(fileUrl)
        console.log('docx预览完成')
      } else if (isExcelFile(fileUrl) && excelContainer.value) {
        if (excelPreviewer) {
          excelPreviewer.destroy?.()
        }
        excelPreviewer = jsPreviewExcel.init(excelContainer.value)
        await excelPreviewer.preview(fileUrl)
        console.log('excel预览完成')
      } else if (isPdfFile(fileUrl) && pdfContainer.value) {
        if (pdfPreviewer) {
          pdfPreviewer.destroy?.()
        }
        pdfPreviewer = jsPreviewPdf.init(pdfContainer.value, {
          onError: (e: any) => {
            console.log('pdf预览发生错误', e)
            handlePreviewError()
          },
          onRendered: () => {
            console.log('pdf渲染完成')
          }
        })
        await pdfPreviewer.preview(fileUrl)
        console.log('pdf预览完成')
      } else if (isPptxFile(fileUrl) && pptxContainer.value) {
        // 清理之前的内容
        pptxContainer.value.innerHTML = ''

        try {
          // 对于PPTX文件，我们提供几种预览方案
          const pptxPreviewHtml = `
            <div class="pptx-preview-wrapper">
              <div class="pptx-preview-header">
                <h3>PowerPoint 文件预览</h3>
                <p>由于浏览器限制，PPTX文件无法直接预览，请选择以下方式：</p>
              </div>
              <div class="pptx-preview-options">
                <button class="preview-btn office-online" onclick="window.open('https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}', '_blank')">
                  <i class="icon-preview"></i>
                  使用 Office Online 预览
                </button>
                <button class="preview-btn google-docs" onclick="window.open('https://docs.google.com/gview?url=${encodeURIComponent(fileUrl)}&embedded=true', '_blank')">
                  <i class="icon-preview"></i>
                  使用 Google Docs 预览
                </button>
                <button class="preview-btn download" onclick="window.open('${fileUrl}', '_blank')">
                  <i class="icon-download"></i>
                  下载文件
                </button>
              </div>
              <div class="pptx-preview-note">
                <p><strong>提示：</strong></p>
                <ul>
                  <li>Office Online 预览：需要文件可公开访问</li>
                  <li>Google Docs 预览：支持大部分PPTX文件</li>
                  <li>下载文件：在本地使用PowerPoint打开</li>
                </ul>
              </div>
            </div>
          `

          // 渲染到容器
          pptxContainer.value.innerHTML = pptxPreviewHtml
          console.log('pptx预览选项已显示')
        } catch (pptxError) {
          console.error('PPTX预览选项显示失败:', pptxError)
          // PPTX预览失败时，直接使用iframe预览
          handlePreviewError()
        }
      }
    } catch (error) {
      console.error('预览失败:', error)
      handlePreviewError()
    }
  }

  // 处理预览错误
  const handlePreviewError = () => {
    console.log("文档渲染失败，回退到iframe预览")
    ElMessage({
      message: "使用备用预览方式",
      type: "warning"
    })
    useIframePreview.value = true
  }

  // const videoFormats = ["mp4", "avi", "mov", "wmv"]
  const fileLoad = (item: any) => {
    meansItem.value = item
    likeFlag.value = item.likeFlag
    dialogFormVisible.value = true

    // 使用nextTick确保DOM已更新
    nextTick(async () => {
      downloadUrl.value = item.manageAddress
      if (item.manageId) primaryKeyId.value = item.manageId

      // 设置iframe预览URL
      iframeUrl.value =
        `${kkFileURL}/onlinePreview?url=` + encodeURIComponent(Base64.encode(item.manageAddress))

      // 如果是支持的office文件，使用js-preview或pptx2html
      if (isDocxFile(item.manageAddress) || isExcelFile(item.manageAddress) || isPdfFile(item.manageAddress) || isPptxFile(item.manageAddress)) {
        // 再次使用nextTick确保容器DOM已渲染
        await nextTick()
        await initPreviewer(item.manageAddress)
      }
    })
  }

  interface Emit {
    (e: "clickDownload", value?: any): void
    (e: "closeDialog", value?: any): void
  }
  const emit = defineEmits<Emit>()
  const download = () => {
    window.open(downloadUrl.value, "_blank")
    emit("clickDownload", primaryKeyId.value)
  }

  // 清理预览器
  const cleanupPreviewers = () => {
    if (docxPreviewer) {
      docxPreviewer.destroy?.()
      docxPreviewer = null
    }
    if (excelPreviewer) {
      excelPreviewer.destroy?.()
      excelPreviewer = null
    }
    if (pdfPreviewer) {
      pdfPreviewer.destroy?.()
      pdfPreviewer = null
    }
    // 清理PPTX容器内容
    if (pptxContainer.value) {
      pptxContainer.value.innerHTML = ''
    }
  }

  const close = () => {
    // 清理预览器
    cleanupPreviewers()
    // 清空数据，避免组件渲染问题
    downloadUrl.value = ""
    iframeUrl.value = ""
    useIframePreview.value = false
    dialogFormVisible.value = false
    emit("closeDialog")
  }

  const support = async () => {
    if (likeFlag.value) {
      await editRelateNumber({ manageId: primaryKeyId.value, likeNumber: -1 })
      ElMessage({
        message: "取消点赞成功！",
        type: "success"
      })
    } else {
      await editRelateNumber({ manageId: primaryKeyId.value, likeNumber: 1 })
      ElMessage({
        message: "点赞成功！",
        type: "success"
      })
    }
    likeFlag.value = !likeFlag.value
  }

  // 是否使用iframe预览的标志
  const useIframePreview = ref(false)

  // 组件卸载时清理预览器
  onUnmounted(() => {
    cleanupPreviewers()
  })

  defineExpose({
    fileLoad
  })
</script>

<style scoped lang="less">
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .downloadBtn {
      margin-right: 30px;
      vertical-align: middle;
      > i {
        margin-right: 50px;
        cursor: pointer;
      }
    }
  }

  .active-collect {
    color: #dc4853 !important;
    transition: all 0.3s;
  }
  .icon-xiazai {
    color: #2695f9;
  }
  .icon-xihuan {
    color: #9c9c9c;
  }

  .office-preview-container {
    width: 100%;
    height: calc(100vh - 60px);
    position: relative;
  }

  .js-preview-container {
    width: 100%;
    height: calc(100vh - 60px);
    overflow: auto;
  }

  .pptx-container {
    background: #f5f5f5;
    padding: 20px;

    :deep(.pptx-preview-wrapper) {
      max-width: 600px;
      margin: 0 auto;
      background: white;
      border-radius: 8px;
      padding: 30px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }

    :deep(.pptx-preview-header) {
      text-align: center;
      margin-bottom: 30px;

      h3 {
        color: #333;
        margin-bottom: 10px;
        font-size: 24px;
      }

      p {
        color: #666;
        font-size: 14px;
      }
    }

    :deep(.pptx-preview-options) {
      display: flex;
      flex-direction: column;
      gap: 15px;
      margin-bottom: 30px;
    }

    :deep(.preview-btn) {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      padding: 15px 20px;
      border: 2px solid #e1e5e9;
      border-radius: 6px;
      background: white;
      color: #333;
      text-decoration: none;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        background: #f0f9ff;
        color: #409eff;
      }

      &.office-online:hover {
        border-color: #d83b01;
        background: #fff5f5;
        color: #d83b01;
      }

      &.google-docs:hover {
        border-color: #4285f4;
        background: #f8fbff;
        color: #4285f4;
      }

      &.download:hover {
        border-color: #67c23a;
        background: #f0f9ff;
        color: #67c23a;
      }
    }

    :deep(.pptx-preview-note) {
      background: #f8f9fa;
      border-radius: 6px;
      padding: 20px;
      border-left: 4px solid #409eff;

      p {
        margin: 0 0 10px 0;
        font-weight: 600;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 20px;

        li {
          color: #666;
          font-size: 14px;
          line-height: 1.6;
          margin-bottom: 5px;
        }
      }
    }
  }
</style>
