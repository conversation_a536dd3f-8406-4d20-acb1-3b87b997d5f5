/**
 * 隐藏手机号码部分信息
 * @param phone 手机号码
 */
export const hidePhone = (phone: string) => {
  // 正则写法
  // return phone.replace(/^(\d{3})(\d{4})(\d{4}$)/,'$1****$3')
  // 字符串截取写法
  return phone.slice(0, 3) + "****" + phone.slice(-4)
}

// 定义允许的文件扩展名
const excelExtensions = ["xls", "xlsx"]
export function isExcelFile(fileName: string): boolean {
  // 获取文件扩展名
  const fileExtension = fileName.split(".").pop()?.toLowerCase()

  // 检查扩展名是否在允许的列表中
  if (!fileExtension) return false

  return excelExtensions.includes(fileExtension)
}
export function isPdfFile(fileName: string): boolean {
  // 获取文件扩展名
  const fileExtension = fileName.split(".").pop()?.toLowerCase()

  // 检查扩展名是否在允许的列表中
  if (!fileExtension) return false

  return fileExtension === "pdf"
}

export function isPptxFile(fileName: string): boolean {
  // 获取文件扩展名
  const fileExtension = fileName.split(".").pop()?.toLowerCase()

  // 检查扩展名是否在允许的列表中
  if (!fileExtension) return false

  return fileExtension === "pptx"
}

/**
 * 判断是否是 .docx 文件（仅支持新版本Word格式）
 * @param fileName 文件名
 * @returns 如果是 .docx 格式，返回 true；否则返回 false
 * @note vue-office-docx 组件仅支持 .docx 格式，不支持 .doc 格式
 */
export function isDocxFile(fileName: string): boolean {
  // 获取文件扩展名
  const fileExtension = fileName.split(".").pop()?.toLowerCase()

  // 检查扩展名是否为 docx
  if (!fileExtension) return false

  return fileExtension === "docx"
}

/**
 * 判断是否是 .doc 文件（老版本Word格式）
 * @param fileName 文件名
 * @returns 如果是 .doc 格式，返回 true；否则返回 false
 * @note .doc 格式不支持在线预览，建议直接下载
 */
export function isDocFile(fileName: string): boolean {
  // 获取文件扩展名
  const fileExtension = fileName.split(".").pop()?.toLowerCase()

  // 检查扩展名是否为 doc
  if (!fileExtension) return false

  return fileExtension === "doc"
}

// 是否是图片链接
export function isImgPath(path: string): boolean {
  return /(https?:\/\/|data:image\/).*?\.(png|jpg|jpeg|gif|svg|webp|ico)/gi.test(path)
}
